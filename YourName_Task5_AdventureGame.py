#!/usr/bin/env python3
"""
Whispers of Eldoria - A Text-Based Adventure Game

A comprehensive choose-your-own-adventure game demonstrating professional
software development practices including proper documentation, error handling,
modular design, and comprehensive testing.

Author: [Your Name] - SIC School Saigon
Course: General Computer Science (GECSC) Year 11
Task: Task 5 - Text-Based Adventure Game (30% of total grade)
Version: 1.0
Python Version: 3.8+
"""

import os
import sys
from typing import Dict, List, Optional, Tuple


class GameState:
    """
    Manages the current state of the game including player progress,
    choices made, and character attributes.
    """
    
    def __init__(self) -> None:
        """Initialize a new game state with default values."""
        self.current_scene: str = "forest_entrance"
        self.player_name: str = ""
        self.choices_made: List[int] = []
        self.has_magic_item: bool = False
        self.character_trait: str = ""
        self.ending_type: str = ""
    
    def reset(self) -> None:
        """Reset the game state for a new playthrough."""
        self.__init__()


class WhispersOfEldoria:
    """
    Main game class containing all game logic, story content, and user interaction.
    Implements a complete text-based adventure with multiple endings.
    """
    
    def __init__(self) -> None:
        """Initialize the game with a fresh game state."""
        self.game_state = GameState()
        self.valid_scenes = {
            "forest_entrance", "ancient_ruins", "mystical_cave", 
            "wizard_tower", "final_confrontation", "game_over"
        }
    
    def run(self) -> None:
        """
        Main game loop that handles the complete game flow including
        welcome, gameplay, and restart functionality.
        """
        try:
            self.display_welcome()
            
            # Main game loop with replay functionality
            while True:
                self.game_state.reset()
                self.get_player_name()
                self.play_game()
                
                if not self.ask_play_again():
                    break
            
            self.display_goodbye()
            
        except KeyboardInterrupt:
            print("\n\nGame interrupted by user. Goodbye!")
        except Exception as e:
            self.handle_unexpected_error(str(e))
    
    def get_player_name(self) -> None:
        """
        Prompt the user for their character name with input validation.
        Ensures name is between 1-20 characters and contains valid characters.
        """
        while True:
            try:
                name = input("Enter your character's name (1-20 characters): ").strip()
                
                if self.validate_name(name):
                    self.game_state.player_name = name
                    print(f"\nWelcome to Eldoria, {name}! Your adventure begins...\n")
                    break
                else:
                    print("Invalid name. Please use 1-20 alphanumeric characters and spaces only.")
                    
            except (EOFError, KeyboardInterrupt):
                raise
            except Exception:
                print("Error reading input. Please try again.")
    
    def validate_name(self, name: str) -> bool:
        """
        Validate player name according to game requirements.

        Args:
            name: The name string to validate

        Returns:
            bool: True if name is valid, False otherwise
        """
        if not name or len(name) < 1 or len(name) > 20:
            return False

        # Reject names that are only whitespace
        if name.strip() == "":
            return False

        # Check for valid characters (alphanumeric and regular spaces only, no tabs/newlines)
        return all(char.isalnum() or char == ' ' for char in name)
    
    def play_game(self) -> None:
        """
        Main gameplay loop that handles scene display, choice processing,
        and game state updates until the game reaches an ending.
        """
        while self.game_state.current_scene != "game_over":
            try:
                self.validate_game_state()
                
                # Display current scene
                self.display_scene(self.game_state.current_scene)
                
                # Get and display available choices
                available_choices = self.get_scene_choices(self.game_state.current_scene)
                self.display_choices(available_choices)
                
                # Get valid player choice
                player_choice = self.get_valid_choice(len(available_choices))
                
                # Process the choice and update game state
                self.process_choice(player_choice)
                self.update_game_state(player_choice)
                
            except Exception as e:
                self.handle_unexpected_error(f"Error during gameplay: {str(e)}")
                break
        
        # Display the final ending
        self.display_ending()
    
    def get_valid_choice(self, max_choice: int) -> int:
        """
        Get a valid choice from the user with input validation and error handling.
        
        Args:
            max_choice: Maximum valid choice number
            
        Returns:
            int: Valid choice number (1-based)
        """
        while True:
            try:
                user_input = input(f"Enter your choice (1-{max_choice}): ").strip()
                
                if self.validate_choice_input(user_input, max_choice):
                    return int(user_input)
                else:
                    print(f"Invalid choice. Please enter a number between 1 and {max_choice}.")
                    
            except (EOFError, KeyboardInterrupt):
                raise
            except ValueError:
                print(f"Please enter a valid number between 1 and {max_choice}.")
            except Exception:
                print("Error reading input. Please try again.")
    
    def validate_choice_input(self, user_input: str, max_value: int) -> bool:
        """
        Validate user choice input for correctness.

        Args:
            user_input: Raw user input string
            max_value: Maximum valid choice value

        Returns:
            bool: True if input is valid, False otherwise
        """
        # Reject inputs with any whitespace characters
        if any(char.isspace() for char in user_input):
            return False

        try:
            choice_num = int(user_input)
            return 1 <= choice_num <= max_value
        except ValueError:
            return False
    
    def display_scene(self, scene_name: str) -> None:
        """
        Display the narrative text for the current scene.
        
        Args:
            scene_name: Name of the scene to display
        """
        self.display_divider()
        
        scene_methods = {
            "forest_entrance": self.display_forest_entrance,
            "ancient_ruins": self.display_ancient_ruins,
            "mystical_cave": self.display_mystical_cave,
            "wizard_tower": self.display_wizard_tower,
            "final_confrontation": self.display_final_confrontation
        }
        
        if scene_name in scene_methods:
            scene_methods[scene_name]()
        else:
            print(f"Error: Unknown scene '{scene_name}'")
    
    def get_scene_choices(self, scene_name: str) -> List[str]:
        """
        Get the available choices for the current scene.
        
        Args:
            scene_name: Name of the current scene
            
        Returns:
            List[str]: List of available choice descriptions
        """
        choices_map = {
            "forest_entrance": [
                "Investigate the glowing mushrooms",
                "Follow the mysterious footprints",
                "Climb the ancient oak tree",
                "Head toward the distant ruins"
            ],
            "ancient_ruins": [
                "Enter the crumbling temple",
                "Search the overgrown courtyard",
                "Examine the mystical symbols"
            ],
            "mystical_cave": [
                "Touch the glowing crystal",
                "Read the ancient inscriptions",
                "Follow the underground river"
            ],
            "wizard_tower": [
                "Knock on the tower door",
                "Climb the exterior wall",
                "Call out to the wizard"
            ],
            "final_confrontation": [
                "Continue"
            ]
        }
        
        return choices_map.get(scene_name, ["Continue"])
    
    def display_choices(self, choices: List[str]) -> None:
        """
        Display the available choices in a formatted menu.
        
        Args:
            choices: List of choice descriptions to display
        """
        print("\nWhat do you choose to do?\n")
        
        for i, choice in enumerate(choices, 1):
            print(f"{i}. {choice}")
        print()
    
    def process_choice(self, choice_number: int) -> None:
        """
        Process the player's choice and update game state accordingly.
        
        Args:
            choice_number: The choice number selected by the player (1-based)
        """
        current_scene = self.game_state.current_scene
        
        # Route to appropriate choice processor
        choice_processors = {
            "forest_entrance": self.process_forest_choice,
            "ancient_ruins": self.process_ruins_choice,
            "mystical_cave": self.process_cave_choice,
            "wizard_tower": self.process_tower_choice,
            "final_confrontation": self.process_final_choice
        }
        
        if current_scene in choice_processors:
            choice_processors[current_scene](choice_number)
        
        # Record the choice
        self.game_state.choices_made.append(choice_number)
    
    def update_game_state(self, choice: int) -> None:
        """
        Update the overall game state based on player progress.

        Args:
            choice: The choice number that was just processed
        """
        # Check if we should proceed to final confrontation
        # The choice has already been added to choices_made in process_choice
        if len(self.game_state.choices_made) >= 3 and self.game_state.current_scene != "final_confrontation":
            self.game_state.current_scene = "final_confrontation"

        # Determine ending type if at final confrontation and this is a final choice
        elif self.game_state.current_scene == "final_confrontation":
            self.determine_ending_type()
            self.game_state.current_scene = "game_over"

    def process_forest_choice(self, choice: int) -> None:
        """Process player choice in the forest entrance scene."""
        if choice == 1:  # Glowing mushrooms
            print("\nYou approach the mysterious glowing mushrooms. As you draw near,")
            print("their ethereal light seems to pulse with your heartbeat. You feel")
            print("a strange tingling sensation, and suddenly your intuition sharpens")
            print("dramatically. You can sense the magical energies flowing through Eldoria!")
            self.game_state.character_trait = "intuitive"
            self.game_state.current_scene = "mystical_cave"

        elif choice == 2:  # Mysterious footprints
            print("\nYou kneel down to examine the strange footprints. They're unlike")
            print("anything you've seen before - too large for human, too small for giant.")
            print("Following them carefully through the underbrush, your tracking skills")
            print("lead you to an ancient wizard's tower rising from the mist.")
            self.game_state.character_trait = "tracker"
            self.game_state.current_scene = "wizard_tower"

        elif choice == 3:  # Ancient oak tree
            print("\nYou climb the massive ancient oak, its bark rough beneath your hands.")
            print("From the canopy, you gain a breathtaking view of Eldoria. Your keen")
            print("observation reveals hidden ruins in the distance, and nestled in the")
            print("tree's hollow, you discover a shimmering magical amulet!")
            self.game_state.has_magic_item = True
            self.game_state.character_trait = "observant"
            self.game_state.current_scene = "ancient_ruins"

        elif choice == 4:  # Distant ruins
            print("\nWith bold determination, you stride directly toward the mysterious")
            print("ruins. Your courage serves you well as you navigate the treacherous")
            print("terrain without hesitation. The ancient stones seem to whisper")
            print("secrets of power as you approach their weathered walls.")
            self.game_state.character_trait = "bold"
            self.game_state.current_scene = "ancient_ruins"

    def process_ruins_choice(self, choice: int) -> None:
        """Process player choice in the ancient ruins scene."""
        if choice == 1:  # Enter temple
            print("\nYou step through the crumbling temple entrance. Inside, ancient")
            print("murals depict the rise and fall of a magical civilization. The air")
            print("hums with residual magic, and you feel your understanding of")
            print("Eldoria's history deepen significantly.")

        elif choice == 2:  # Search courtyard
            print("\nYou carefully search the overgrown courtyard, pushing aside")
            print("centuries of vines and debris. Hidden beneath the vegetation,")
            print("you discover a pristine magical fountain that still flows with")
            print("crystal-clear water imbued with healing properties.")

        elif choice == 3:  # Examine symbols
            print("\nThe mystical symbols carved into the stone walls seem to shift")
            print("and dance before your eyes. As you study them intently, their")
            print("meaning becomes clear - they're a map showing the location of")
            print("Eldoria's greatest magical treasure!")

    def process_cave_choice(self, choice: int) -> None:
        """Process player choice in the mystical cave scene."""
        if choice == 1:  # Touch crystal
            print("\nYou reach out and touch the massive glowing crystal. Immediately,")
            print("visions flood your mind - images of Eldoria's past, present, and")
            print("possible futures. The crystal's power flows through you, enhancing")
            print("your magical abilities beyond anything you thought possible.")

        elif choice == 2:  # Read inscriptions
            print("\nThe ancient inscriptions glow softly as you read them aloud.")
            print("They tell the story of the first mages who discovered Eldoria")
            print("and bound their essence to the land itself. You feel a deep")
            print("connection to this ancient wisdom forming within you.")

        elif choice == 3:  # Follow river
            print("\nYou follow the underground river as it winds deeper into the")
            print("earth. The water glows with bioluminescent magic, lighting your")
            print("way to a hidden chamber where the very source of Eldoria's")
            print("magic springs from the living rock.")

    def process_tower_choice(self, choice: int) -> None:
        """Process player choice in the wizard tower scene."""
        if choice == 1:  # Knock on door
            print("\nYou knock respectfully on the ancient wooden door. After a moment,")
            print("it creaks open to reveal a wise old wizard with twinkling eyes.")
            print("'I've been expecting you,' he says with a knowing smile. 'Your")
            print("destiny in Eldoria has been written in the stars.'")

        elif choice == 2:  # Climb wall
            print("\nUsing your agility and determination, you scale the tower's")
            print("exterior wall. Through a window, you glimpse the wizard's study")
            print("filled with floating books and swirling magical energies. The")
            print("wizard notices you and chuckles at your unconventional approach.")

        elif choice == 3:  # Call out
            print("\nYou call out to the tower, your voice echoing across the mystical")
            print("landscape. From the highest window, the wizard appears and waves")
            print("his staff. Suddenly, you find yourself magically transported")
            print("inside the tower, surrounded by wonders beyond imagination.")

    def process_final_choice(self, choice: int) -> None:
        """Process the final confrontation choice."""
        print(f"\n{self.game_state.player_name}, your journey through Eldoria has")
        print("led you to this moment of destiny. The choices you've made have")
        print("shaped not only your character but the very fate of this mystical realm...")

    def determine_ending_type(self) -> None:
        """
        Determine the appropriate ending based on player choices and character traits.
        Uses a scoring system to evaluate the player's journey.
        """
        magic_score = 0
        wisdom_score = 0
        courage_score = 0

        # Analyze choice patterns (simplified scoring)
        for i, choice in enumerate(self.game_state.choices_made):
            if i == 0:  # Forest choices
                if choice in [1, 3]:  # Mushrooms or tree (magical/observant)
                    magic_score += 1
                elif choice == 2:  # Footprints (tracking/wisdom)
                    wisdom_score += 1
                elif choice == 4:  # Direct to ruins (bold)
                    courage_score += 1
            else:  # Subsequent choices add to various scores
                magic_score += choice % 2  # Odd choices favor magic
                wisdom_score += (choice + 1) % 2  # Even choices favor wisdom
                courage_score += choice // 2  # Higher choices favor courage

        # Factor in character traits
        trait_bonuses = {
            "intuitive": ("magic", 2),
            "observant": ("wisdom", 2),
            "bold": ("courage", 2),
            "tracker": ("wisdom", 1)
        }

        if self.game_state.character_trait in trait_bonuses:
            trait_type, bonus = trait_bonuses[self.game_state.character_trait]
            if trait_type == "magic":
                magic_score += bonus
            elif trait_type == "wisdom":
                wisdom_score += bonus
            elif trait_type == "courage":
                courage_score += bonus

        # Magic item bonus
        if self.game_state.has_magic_item:
            magic_score += 1

        # Determine ending based on highest score
        if magic_score > wisdom_score and magic_score > courage_score:
            self.game_state.ending_type = "arcane_master"
        elif wisdom_score > courage_score:
            self.game_state.ending_type = "wise_scholar"
        elif courage_score > 0:
            self.game_state.ending_type = "brave_hero"
        else:
            self.game_state.ending_type = "cautious_survivor"

    # Display Methods
    def display_welcome(self) -> None:
        """Display the game's welcome screen and introduction."""
        self.clear_screen()
        print("╔══════════════════════════════════════════════════════════════╗")
        print("║                    WHISPERS OF ELDORIA                       ║")
        print("║                A Mystical Text Adventure                     ║")
        print("╚══════════════════════════════════════════════════════════════╝")
        print()
        print("Welcome to the mystical realm of Eldoria, where ancient magic")
        print("flows through enchanted forests and forgotten ruins hold")
        print("secrets of power beyond imagination...")
        print()
        print("Your choices will shape your destiny and determine the fate")
        print("of this magical land. Choose wisely, for every decision")
        print("carries consequences that echo through eternity.")
        print()

    def display_divider(self) -> None:
        """Display a visual divider between game sections."""
        print()
        print("═══════════════════════════════════════════════════════════════")
        print()

    def display_forest_entrance(self) -> None:
        """Display the forest entrance scene."""
        print(f"🌲 THE ENCHANTED FOREST ENTRANCE 🌲")
        print()
        print(f"{self.game_state.player_name}, you stand at the edge of the mystical")
        print("Eldorian forest. Ancient trees tower above you, their leaves")
        print("shimmering with an otherworldly light. The air itself seems")
        print("alive with magic, and you can feel the weight of destiny")
        print("pressing upon your shoulders.")
        print()
        print("Before you lie several paths:")
        print("• Glowing mushrooms pulse with ethereal light to your left")
        print("• Mysterious footprints lead deeper into the forest")
        print("• An ancient oak tree beckons with its massive, climbable trunk")
        print("• Distant ruins are barely visible through the morning mist")

    def display_ancient_ruins(self) -> None:
        """Display the ancient ruins scene."""
        print("🏛️ THE ANCIENT RUINS 🏛️")
        print()
        print("You arrive at the weathered remains of a once-great civilization.")
        print("Crumbling stone walls covered in mystical symbols stretch toward")
        print("the sky, while an overgrown courtyard hints at former grandeur.")
        print("The very air thrums with residual magical energy, and you sense")
        print("that great secrets lie hidden within these ancient stones.")
        print()
        print("The ruins offer several areas to explore:")
        print("• A partially collapsed temple with intricate carvings")
        print("• An overgrown courtyard filled with mysterious vegetation")
        print("• Walls covered in glowing mystical symbols")

    def display_mystical_cave(self) -> None:
        """Display the mystical cave scene."""
        print("🔮 THE MYSTICAL CAVE 🔮")
        print()
        print("You enter a vast underground cavern illuminated by a massive")
        print("crystal formation that pulses with inner light. Ancient")
        print("inscriptions cover the walls, and an underground river flows")
        print("through the chamber, its waters glowing with magical energy.")
        print("This place feels like the very heart of Eldoria's power.")
        print()
        print("Three wonders draw your attention:")
        print("• A towering crystal that hums with magical energy")
        print("• Ancient inscriptions that seem to shift and move")
        print("• A glowing underground river flowing into darkness")

    def display_wizard_tower(self) -> None:
        """Display the wizard tower scene."""
        print("🗼 THE WIZARD'S TOWER 🗼")
        print()
        print("Before you rises an impossibly tall tower that seems to pierce")
        print("the very clouds. Its stones are covered in moving runes, and")
        print("magical energy crackles around its peak. You sense that within")
        print("this tower dwells one of the most powerful beings in all of")
        print("Eldoria - a wizard of immense knowledge and ability.")
        print()
        print("You consider your approach:")
        print("• Knock politely on the ornate wooden door")
        print("• Attempt to climb the tower's exterior wall")
        print("• Call out to announce your presence")

    def display_final_confrontation(self) -> None:
        """Display the final confrontation scene."""
        print("⚡ THE MOMENT OF DESTINY ⚡")
        print()
        print(f"{self.game_state.player_name}, your journey through the mystical")
        print("realm of Eldoria has brought you to this pivotal moment.")
        print("The magical energies you've encountered, the wisdom you've")
        print("gained, and the courage you've shown have all led to this:")
        print()
        print("Before you materializes the Guardian of Eldoria, an ancient")
        print("being of pure magical energy who has watched over this realm")
        print("since time immemorial. The Guardian speaks:")
        print()
        print("'Young traveler, your actions have been observed. The path")
        print("you have chosen and the character you have shown will now")
        print("determine not only your fate, but the future of Eldoria itself.'")
        print()
        print("The air shimmers with possibility as your destiny unfolds...")

    def display_ending(self) -> None:
        """Display the appropriate ending based on the player's journey."""
        self.display_divider()

        ending_methods = {
            "arcane_master": self.display_arcane_master_ending,
            "wise_scholar": self.display_wise_scholar_ending,
            "brave_hero": self.display_brave_hero_ending,
            "cautious_survivor": self.display_cautious_survivor_ending
        }

        if self.game_state.ending_type in ending_methods:
            ending_methods[self.game_state.ending_type]()
        else:
            print("An unexpected ending has occurred.")

        print()
        print("Thank you for playing Whispers of Eldoria!")
        print("Your adventure in this mystical realm has come to an end,")
        print("but the magic of Eldoria will live on in your memories.")

    def display_arcane_master_ending(self) -> None:
        """Display the Arcane Master ending."""
        print("🌟 THE ARCANE MASTER 🌟")
        print()
        print(f"Congratulations, {self.game_state.player_name}!")
        print()
        print("Your deep connection to the magical forces of Eldoria has")
        print("transformed you into a true Arcane Master. The Guardian")
        print("recognizes your affinity for magic and grants you dominion")
        print("over the mystical energies that flow through this realm.")
        print()
        print("You become the new protector of Eldoria's magical balance,")
        print("wielding powers beyond mortal comprehension. The very")
        print("elements bend to your will, and the ancient secrets of")
        print("magic are yours to command.")
        print()
        print("Eldoria flourishes under your magical guidance, and your")
        print("name becomes legend among those who seek the mystical arts.")

    def display_wise_scholar_ending(self) -> None:
        """Display the Wise Scholar ending."""
        print("📚 THE WISE SCHOLAR 📚")
        print()
        print(f"Well done, {self.game_state.player_name}!")
        print()
        print("Your careful observation and thirst for knowledge have")
        print("impressed the Guardian of Eldoria. You are granted access")
        print("to the Great Library of Eldoria, a repository of all")
        print("knowledge gathered since the realm's creation.")
        print()
        print("As the Keeper of Ancient Wisdom, you spend your days")
        print("studying the deepest mysteries of Eldoria and sharing")
        print("your knowledge with worthy seekers. Your wisdom guides")
        print("future generations of adventurers.")
        print()
        print("The realm prospers under your scholarly guidance, and")
        print("peace reigns as conflicts are resolved through wisdom")
        print("rather than force.")

    def display_brave_hero_ending(self) -> None:
        """Display the Brave Hero ending."""
        print("⚔️ THE BRAVE HERO ⚔️")
        print()
        print(f"Excellent, {self.game_state.player_name}!")
        print()
        print("Your courage and bold actions have earned you the title")
        print("of Champion of Eldoria. The Guardian bestows upon you")
        print("the legendary Sword of Starlight, a weapon that can")
        print("cut through any darkness and protect the innocent.")
        print()
        print("You become the defender of all who dwell in Eldoria,")
        print("standing against any threat that would harm this")
        print("mystical realm. Your bravery inspires others to")
        print("stand up for what is right.")
        print()
        print("Under your protection, Eldoria becomes a beacon of")
        print("hope and justice, where the weak are protected and")
        print("evil finds no foothold.")

    def display_cautious_survivor_ending(self) -> None:
        """Display the Cautious Survivor ending."""
        print("🛡️ THE CAUTIOUS SURVIVOR 🛡️")
        print()
        print(f"Wise choice, {self.game_state.player_name}!")
        print()
        print("Your careful and measured approach has served you well.")
        print("The Guardian recognizes your prudence and grants you")
        print("the gift of foresight - the ability to see potential")
        print("dangers before they manifest.")
        print()
        print("You become Eldoria's early warning system, helping")
        print("others avoid the pitfalls and dangers that threaten")
        print("unwary travelers. Your caution saves countless lives")
        print("and prevents many disasters.")
        print()
        print("Though your path was careful rather than bold, your")
        print("contribution to Eldoria's safety is immeasurable, and")
        print("you are honored as the Guardian's Sentinel.")

    # Utility Methods
    def ask_play_again(self) -> bool:
        """
        Ask the player if they want to play again with input validation.

        Returns:
            bool: True if player wants to play again, False otherwise
        """
        while True:
            try:
                response = input("\nWould you like to play again? (y/n): ").strip().lower()

                if response in ['y', 'yes']:
                    return True
                elif response in ['n', 'no']:
                    return False
                else:
                    print("Please enter 'y' for yes or 'n' for no.")

            except (EOFError, KeyboardInterrupt):
                return False
            except Exception:
                print("Error reading input. Please try again.")

    def clear_screen(self) -> None:
        """Clear the console screen in a cross-platform manner."""
        try:
            # Windows
            if os.name == 'nt':
                os.system('cls')
            # Unix/Linux/MacOS
            else:
                os.system('clear')
        except Exception:
            # If clearing fails, just print some newlines
            print('\n' * 50)

    def display_goodbye(self) -> None:
        """Display the final goodbye message."""
        self.clear_screen()
        print("╔══════════════════════════════════════════════════════════════╗")
        print("║                    FAREWELL, ADVENTURER!                     ║")
        print("╚══════════════════════════════════════════════════════════════╝")
        print()
        print("Thank you for exploring the mystical realm of Eldoria!")
        print("May your adventures continue in dreams and imagination...")
        print()
        print("Game created as part of Software Development Cycle demonstration.")
        print("Demonstrating: Planning, Implementation, Testing, and Evaluation")
        print()

    def validate_game_state(self) -> None:
        """
        Validate the current game state for consistency and correctness.

        Raises:
            ValueError: If game state is invalid
        """
        if not hasattr(self, 'game_state') or self.game_state is None:
            raise ValueError("Game state is not initialized")

        if self.game_state.current_scene not in self.valid_scenes:
            raise ValueError(f"Invalid scene: {self.game_state.current_scene}")

        if not isinstance(self.game_state.choices_made, list):
            raise ValueError("Choices made must be a list")

    def handle_unexpected_error(self, error_message: str) -> None:
        """
        Handle unexpected errors gracefully with user-friendly messages.

        Args:
            error_message: Description of the error that occurred
        """
        print(f"\n❌ An unexpected error occurred: {error_message}")
        print("The game will now exit safely.")
        print("Please restart the program to play again.")
        print("\nIf this problem persists, please check that you have:")
        print("- Python 3.8 or higher installed")
        print("- Sufficient system resources available")
        print("- Proper file permissions")

        # Log error for debugging (in a real application)
        # logging.error(f"Game error: {error_message}")

        sys.exit(1)


def main() -> None:
    """
    Main entry point for the Whispers of Eldoria game.
    Handles game initialization and execution.
    """
    try:
        # Check Python version compatibility
        if sys.version_info < (3, 8):
            print("❌ This game requires Python 3.8 or higher.")
            print(f"Current version: {sys.version}")
            print("Please upgrade Python and try again.")
            sys.exit(1)

        # Initialize and run the game
        game = WhispersOfEldoria()
        game.run()

    except KeyboardInterrupt:
        print("\n\n👋 Game interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        print("Please restart the game and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
